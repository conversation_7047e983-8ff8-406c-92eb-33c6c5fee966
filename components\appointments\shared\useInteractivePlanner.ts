"use client";

import { useState, useCallback, useRef, useEffect, useMemo } from "react";
import { toast } from "sonner";
import { checkTimeSlotCapacity } from "@/lib/actions/database";
import { bookSingleAppointment } from "@/lib/actions/appointments";
import { isValidTimeSlot, isValidPackageId } from "../InteractivePlanner";
import type {
  FlexPackageGroup,
  ServerFlexPackageSessionWithDetails,
} from "@/components/appointments/FlexDashboard";
import type { TimeSlotCapacity } from "@/components/appointments/shared/appointment-utils";

// Types for the hook
interface DragState {
  isDragging: boolean;
  draggedPackageId: string | null;
  draggedPackageName: string | null;
}

interface ComponentErrorState {
  hasError: boolean;
  errorMessage: string;
  errorCode?: string;
  retryCount: number;
  lastErrorTime?: number;
}

interface NetworkState {
  isOnline: boolean;
  isSlowConnection: boolean;
}

interface SlotState {
  loading: boolean;
  capacity?: TimeSlotCapacity;
  error?: string;
}

type BookingStep = "idle" | "validating" | "checking" | "booking" | "updating";

interface BookingSuccessData {
  memberPackageId: string;
  sessionDate: string;
  sessionTime: string;
  packageName: string;
}

interface BookingError {
  code: string;
  message: string;
  details?: string[];
}

interface UseInteractivePlannerProps {
  initialFlexPackages: FlexPackageGroup[];
  initialBookedDates: string[];
  onBookingSuccess?: (booking: BookingSuccessData) => void;
  onBookingError?: (error: BookingError) => void;
  maxWeeksAhead?: number;
  maxWeeksBehind?: number;
}

export const useInteractivePlanner = ({
  initialFlexPackages,
  initialBookedDates,
  onBookingSuccess,
  onBookingError,
  maxWeeksAhead = 8,
  maxWeeksBehind = 0,
}: UseInteractivePlannerProps) => {
  // State management
  const [flexPackages, setFlexPackages] = useState<FlexPackageGroup[]>(initialFlexPackages);
  const [bookedDates, setBookedDates] = useState<Set<string>>(new Set(initialBookedDates));
  const [slotState, setSlotState] = useState<Record<string, SlotState>>({});
  const [dragState, setDragState] = useState<DragState>({
    isDragging: false,
    draggedPackageId: null,
    draggedPackageName: null,
  });
  const [weekOffset, setWeekOffset] = useState(0);
  const [isBooking, setIsBooking] = useState(false);
  const [bookingStep, setBookingStep] = useState<BookingStep>("idle");
  const [errorState, setErrorState] = useState<ComponentErrorState>({
    hasError: false,
    errorMessage: "",
    retryCount: 0,
  });
  const [networkState, setNetworkState] = useState<NetworkState>({
    isOnline: navigator.onLine,
    isSlowConnection: false,
  });

  // Refs for performance optimization
  const capacityCache = useRef<Record<string, Record<string, TimeSlotCapacity>>>({});
  const abortController = useRef<AbortController | null>(null);
  const retryTimeouts = useRef<Record<string, NodeJS.Timeout>>({});

  // Memoized calculations
  const stats = useMemo(() => {
    return flexPackages.reduce(
      (acc, pkg) => {
        acc.totalSessions += pkg.totalSessions;
        acc.usedSessions += pkg.usedSessions;
        acc.remainingSessions += pkg.remainingSessions;
        
        const scheduledCount = pkg.sessions.filter((s) => s.status === "scheduled").length;
        const completedCount = pkg.sessions.filter((s) => s.status === "completed").length;
        
        acc.scheduledSessions += scheduledCount;
        acc.completedSessions += completedCount;
        return acc;
      },
      {
        totalSessions: 0,
        usedSessions: 0,
        remainingSessions: 0,
        scheduledSessions: 0,
        completedSessions: 0,
      }
    );
  }, [flexPackages]);

  const weekDays = useMemo(() => {
    const start = new Date();
    start.setHours(0, 0, 0, 0);
    start.setDate(start.getDate() + weekOffset * 7);
    return Array.from({ length: 7 }, (_, i) => {
      const d = new Date(start);
      d.setDate(start.getDate() + i);
      return d.toISOString().split("T")[0];
    });
  }, [weekOffset]);

  const currentWeekDisplay = useMemo(() => {
    const startDate = new Date();
    startDate.setHours(0, 0, 0, 0);
    startDate.setDate(startDate.getDate() + weekOffset * 7);
    const endDate = new Date(startDate);
    endDate.setDate(startDate.getDate() + 6);
    
    return `${startDate.toLocaleDateString('tr-TR', { 
      day: 'numeric', 
      month: 'short' 
    })} - ${endDate.toLocaleDateString('tr-TR', { 
      day: 'numeric', 
      month: 'short',
      year: 'numeric'
    })}`;
  }, [weekOffset]);

  // Network monitoring
  useEffect(() => {
    const handleOnline = () => {
      setNetworkState(prev => ({ ...prev, isOnline: true }));
      setErrorState(prev => ({ ...prev, hasError: false }));
    };
    
    const handleOffline = () => {
      setNetworkState(prev => ({ ...prev, isOnline: false }));
      setErrorState(prev => ({
        ...prev,
        hasError: true,
        errorMessage: "İnternet bağlantısı kesildi",
        errorCode: "NETWORK_ERROR",
      }));
    };

    window.addEventListener('online', handleOnline);
    window.addEventListener('offline', handleOffline);

    return () => {
      window.removeEventListener('online', handleOnline);
      window.removeEventListener('offline', handleOffline);
      
      if (abortController.current) {
        abortController.current.abort();
      }
      
      Object.values(retryTimeouts.current).forEach(clearTimeout);
    };
  }, []);

  // Error handling
  const handleError = useCallback((error: Error, context: string) => {
    console.error(`Error in ${context}:`, error);
    
    const errorMessage = error.message || "Bilinmeyen bir hata oluştu";
    const errorCode = error.name || "UNKNOWN_ERROR";
    
    setErrorState(prev => ({
      hasError: true,
      errorMessage,
      errorCode,
      retryCount: prev.retryCount,
      lastErrorTime: Date.now(),
    }));

    if (!networkState.isOnline) {
      toast.error("İnternet bağlantınızı kontrol edin");
    } else if (errorCode === "AbortError") {
      return;
    } else {
      toast.error(errorMessage);
    }
  }, [networkState.isOnline]);

  // Retry mechanism
  const retryWithBackoff = useCallback((
    operation: () => Promise<void>,
    key: string,
    maxRetries: number = 3
  ) => {
    const currentRetryCount = errorState.retryCount;
    
    if (currentRetryCount >= maxRetries) {
      toast.error("Maksimum deneme sayısına ulaşıldı");
      return;
    }

    const delay = Math.min(1000 * Math.pow(2, currentRetryCount), 10000);
    
    retryTimeouts.current[key] = setTimeout(async () => {
      try {
        setErrorState(prev => ({ ...prev, retryCount: prev.retryCount + 1 }));
        await operation();
        setErrorState(prev => ({ ...prev, hasError: false, retryCount: 0 }));
      } catch (error) {
        handleError(error as Error, `retry-${key}`);
      }
    }, delay);
  }, [errorState.retryCount, handleError]);

  // Navigation handlers
  const handleWeekNavigation = useCallback((direction: 'prev' | 'next') => {
    setWeekOffset((current) => {
      const newOffset = direction === 'prev' ? current - 1 : current + 1;
      
      if (newOffset < -maxWeeksBehind || newOffset > maxWeeksAhead) {
        toast.error(
          direction === 'prev' 
            ? "Daha geçmiş tarihlere gidemezsiniz" 
            : "Daha ileri tarihlere gidemezsiniz"
        );
        return current;
      }
      
      return newOffset;
    });
  }, [maxWeeksBehind, maxWeeksAhead]);

  return {
    // State
    flexPackages,
    setFlexPackages,
    bookedDates,
    setBookedDates,
    slotState,
    setSlotState,
    dragState,
    setDragState,
    weekOffset,
    setWeekOffset,
    isBooking,
    setIsBooking,
    bookingStep,
    setBookingStep,
    errorState,
    setErrorState,
    networkState,
    setNetworkState,
    
    // Computed values
    stats,
    weekDays,
    currentWeekDisplay,
    
    // Refs
    capacityCache,
    abortController,
    retryTimeouts,
    
    // Handlers
    handleError,
    retryWithBackoff,
    handleWeekNavigation,
  };
};
