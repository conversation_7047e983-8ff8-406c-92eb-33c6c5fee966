"use client";

import { motion } from "framer-motion";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { TrendingUp, History } from "lucide-react";
import SessionItem from "./SessionItem";
import type { Member } from "@/lib/supabase/types";
import Link from "next/link";

interface ServerFlexPackageSessionWithDetails {
  id: string;
  member_id: string;
  member_package_id: string;
  session_date: string;
  session_time: string;
  status: "scheduled" | "completed";
  notes?: string | null;
  created_at: string;
  updated_at: string;
  member_package?: {
    flex_package?: {
      id: string;
      name: string;
      description: string | null;
      duration_days: number;
      session_count: number;
      price: number;
      is_active: boolean;
    } | null;
  } | null;
  member?: Member | null;
  packageName?: string;
}

interface CompletedSessionsTimelineProps {
  sessions: ServerFlexPackageSessionWithDetails[];
}

const fadeInUp = {
  initial: { opacity: 0, y: 20 },
  animate: { opacity: 1, y: 0 },
};

export default function CompletedSessionsTimeline({
  sessions,
}: CompletedSessionsTimelineProps) {
  return (
    <motion.section variants={fadeInUp} className="space-y-3">
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-3">
          <div className="w-8 h-8 rounded-lg bg-emerald-500/10 flex items-center justify-center">
            <History className="w-4 h-4 text-emerald-500" />
          </div>
          <div>
            <h2 className="text-lg font-semibold text-foreground">
              Geçmiş Performans
            </h2>
            <p className="text-xs text-muted-foreground">
              Son tamamlanan antrenmanlar
            </p>
          </div>
        </div>
        <Badge variant="outline" className="px-2 py-1 text-xs">
          {sessions.length} Tamamlanmış
        </Badge>
      </div>

      <div className="bg-card border border-border/30 rounded-lg overflow-hidden">
        <div className="p-3">
          {sessions.length === 0 ? (
            <div className="text-center py-6">
              <History className="w-8 h-8 mx-auto text-muted-foreground/40 mb-3" />
              <h3 className="text-sm font-medium text-foreground mb-1">
                Henüz Tamamlanmış Seansınız Yok
              </h3>
              <p className="text-xs text-muted-foreground mb-3">
                İlk antrenmanınızı tamamladığınızda burada görünecek
              </p>
              <Button
                variant="outline"
                size="sm"
                asChild
                className="h-7 text-xs"
              >
                <Link href="/appointments/new">İlk Randevunuzu Alın</Link>
              </Button>
            </div>
          ) : (
            <div className="grid gap-1">
              {sessions.slice(0, 3).map((session, index) => (
                <SessionItem
                  key={session.id}
                  session={session}
                  isHistorical={true}
                  index={index}
                />
              ))}
              {sessions.length > 3 && (
                <div className="text-center pt-2 border-t border-border/30">
                  <Button variant="outline" size="sm" className="h-7 text-xs">
                    <TrendingUp className="w-3 h-3 mr-1" />
                    Tüm Geçmişi Gör ({sessions.length - 3} daha)
                  </Button>
                </div>
              )}
            </div>
          )}
        </div>
      </div>
    </motion.section>
  );
}
