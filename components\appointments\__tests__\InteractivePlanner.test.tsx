/**
 * @jest-environment jsdom
 */

import React from 'react';
import { render, screen, fireEvent, waitFor, act } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { toast } from 'sonner';
import InteractivePlanner from '../InteractivePlanner';
import { checkTimeSlotCapacity } from '@/lib/actions/database';
import { bookSingleAppointment } from '@/lib/actions/appointments';

// Mock dependencies
jest.mock('sonner', () => ({
  toast: {
    error: jest.fn(),
    success: jest.fn(),
    info: jest.fn(),
  },
}));

jest.mock('@/lib/actions/database', () => ({
  checkTimeSlotCapacity: jest.fn(),
}));

jest.mock('@/lib/actions/appointments', () => ({
  bookSingleAppointment: jest.fn(),
}));

jest.mock('framer-motion', () => ({
  motion: {
    div: ({ children, ...props }: any) => <div {...props}>{children}</div>,
    button: ({ children, ...props }: any) => <button {...props}>{children}</button>,
  },
  AnimatePresence: ({ children }: any) => children,
}));

// Mock data
const mockFlexPackages = [
  {
    packageName: 'Test Package 1',
    memberPackage: {
      id: 'pkg-1',
      status: 'active',
    },
    sessions: [],
    totalSessions: 10,
    usedSessions: 3,
    remainingSessions: 7,
    expiryDate: '2024-12-31',
  },
  {
    packageName: 'Test Package 2',
    memberPackage: {
      id: 'pkg-2',
      status: 'active',
    },
    sessions: [],
    totalSessions: 5,
    usedSessions: 1,
    remainingSessions: 4,
    expiryDate: '2024-12-31',
  },
];

const mockBookedDates = ['2024-01-15'];

const defaultProps = {
  initialFlexPackages: mockFlexPackages,
  initialBookedDates: mockBookedDates,
};

describe('InteractivePlanner', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    // Mock navigator.onLine
    Object.defineProperty(navigator, 'onLine', {
      writable: true,
      value: true,
    });
  });

  describe('Rendering', () => {
    it('renders the component with header and calendar', () => {
      render(<InteractivePlanner {...defaultProps} />);
      
      expect(screen.getByText('Akıllı Planlayıcı')).toBeInTheDocument();
      expect(screen.getByText('Paket haklarınızı takvim üzerinde sürükle-bırak ile planlayın')).toBeInTheDocument();
      expect(screen.getByRole('grid')).toBeInTheDocument();
    });

    it('renders package cards with correct information', () => {
      render(<InteractivePlanner {...defaultProps} />);
      
      expect(screen.getByText('Test Package 1')).toBeInTheDocument();
      expect(screen.getByText('Test Package 2')).toBeInTheDocument();
      expect(screen.getByText('7 hak')).toBeInTheDocument();
      expect(screen.getByText('4 hak')).toBeInTheDocument();
    });

    it('displays keyboard shortcuts help', () => {
      render(<InteractivePlanner {...defaultProps} />);
      
      const shortcutsToggle = screen.getByText('Klavye kısayolları');
      expect(shortcutsToggle).toBeInTheDocument();
    });
  });

  describe('Navigation', () => {
    it('navigates to previous week when clicking previous button', async () => {
      const user = userEvent.setup();
      render(<InteractivePlanner {...defaultProps} />);
      
      const prevButton = screen.getByLabelText(/önceki hafta/i);
      await user.click(prevButton);
      
      // Should update the week display
      expect(screen.getByRole('grid')).toBeInTheDocument();
    });

    it('navigates to next week when clicking next button', async () => {
      const user = userEvent.setup();
      render(<InteractivePlanner {...defaultProps} />);
      
      const nextButton = screen.getByLabelText(/sonraki hafta/i);
      await user.click(nextButton);
      
      expect(screen.getByRole('grid')).toBeInTheDocument();
    });

    it('returns to current week when clicking today button', async () => {
      const user = userEvent.setup();
      render(<InteractivePlanner {...defaultProps} />);
      
      const todayButton = screen.getByText('Bugün');
      await user.click(todayButton);
      
      expect(screen.getByRole('grid')).toBeInTheDocument();
    });
  });

  describe('Keyboard Navigation', () => {
    it('navigates weeks with keyboard shortcuts', async () => {
      const user = userEvent.setup();
      render(<InteractivePlanner {...defaultProps} />);
      
      const container = screen.getByRole('application');
      container.focus();
      
      // Test Ctrl+ArrowRight for next week
      await user.keyboard('{Control>}{ArrowRight}{/Control}');
      expect(screen.getByRole('grid')).toBeInTheDocument();
      
      // Test Ctrl+ArrowLeft for previous week
      await user.keyboard('{Control>}{ArrowLeft}{/Control}');
      expect(screen.getByRole('grid')).toBeInTheDocument();
    });

    it('goes to current week with Ctrl+Home', async () => {
      const user = userEvent.setup();
      render(<InteractivePlanner {...defaultProps} />);
      
      const container = screen.getByRole('application');
      container.focus();
      
      await user.keyboard('{Control>}{Home}{/Control}');
      expect(toast.info).toHaveBeenCalledWith('Bu haftaya dönüldü');
    });
  });

  describe('Drag and Drop', () => {
    it('starts drag operation when dragging package button', async () => {
      render(<InteractivePlanner {...defaultProps} />);
      
      const dragButton = screen.getAllByText('Sürükleyin')[0];
      
      fireEvent.dragStart(dragButton, {
        dataTransfer: {
          setData: jest.fn(),
          effectAllowed: '',
        },
      });
      
      expect(screen.getByText(/sürükleniyor/i)).toBeInTheDocument();
    });

    it('prevents drop on disabled slots', async () => {
      render(<InteractivePlanner {...defaultProps} />);
      
      // Find a time slot button (they should be disabled for past times)
      const timeSlots = screen.getAllByRole('button').filter(
        button => button.getAttribute('title')?.includes(':')
      );
      
      if (timeSlots.length > 0) {
        const slot = timeSlots[0];
        
        fireEvent.dragOver(slot);
        fireEvent.drop(slot, {
          dataTransfer: {
            getData: () => 'pkg-1',
          },
        });
        
        // Should not trigger booking for disabled slots
        expect(bookSingleAppointment).not.toHaveBeenCalled();
      }
    });
  });

  describe('Capacity Checking', () => {
    it('fetches capacity when hovering over time slots', async () => {
      const mockCapacity = {
        currentCount: 1,
        maxCapacity: 3,
        available: true,
      };
      
      (checkTimeSlotCapacity as jest.Mock).mockResolvedValue(mockCapacity);
      
      render(<InteractivePlanner {...defaultProps} />);
      
      const timeSlots = screen.getAllByRole('button').filter(
        button => button.getAttribute('title')?.includes(':')
      );
      
      if (timeSlots.length > 0) {
        const slot = timeSlots[0];
        
        fireEvent.mouseEnter(slot);
        
        await waitFor(() => {
          expect(checkTimeSlotCapacity).toHaveBeenCalled();
        });
      }
    });

    it('displays capacity information correctly', async () => {
      const mockCapacity = {
        currentCount: 2,
        maxCapacity: 3,
        available: true,
      };
      
      (checkTimeSlotCapacity as jest.Mock).mockResolvedValue(mockCapacity);
      
      render(<InteractivePlanner {...defaultProps} />);
      
      const timeSlots = screen.getAllByRole('button').filter(
        button => button.getAttribute('title')?.includes(':')
      );
      
      if (timeSlots.length > 0) {
        const slot = timeSlots[0];
        
        fireEvent.mouseEnter(slot);
        
        await waitFor(() => {
          expect(screen.getByText('2/3')).toBeInTheDocument();
        });
      }
    });
  });

  describe('Error Handling', () => {
    it('handles network errors gracefully', async () => {
      (checkTimeSlotCapacity as jest.Mock).mockRejectedValue(
        new Error('Network error')
      );
      
      render(<InteractivePlanner {...defaultProps} />);
      
      const timeSlots = screen.getAllByRole('button').filter(
        button => button.getAttribute('title')?.includes(':')
      );
      
      if (timeSlots.length > 0) {
        const slot = timeSlots[0];
        
        fireEvent.mouseEnter(slot);
        
        await waitFor(() => {
          expect(toast.error).toHaveBeenCalledWith('Network error');
        });
      }
    });

    it('shows offline indicator when network is down', async () => {
      // Mock offline state
      Object.defineProperty(navigator, 'onLine', {
        writable: true,
        value: false,
      });
      
      render(<InteractivePlanner {...defaultProps} />);
      
      // Trigger offline event
      act(() => {
        window.dispatchEvent(new Event('offline'));
      });
      
      await waitFor(() => {
        expect(screen.getByText('Çevrimdışı')).toBeInTheDocument();
      });
    });
  });

  describe('Booking Process', () => {
    it('successfully books an appointment', async () => {
      const mockBookingResult = {
        success: true,
        message: 'Booking successful',
      };
      
      (bookSingleAppointment as jest.Mock).mockResolvedValue(mockBookingResult);
      (checkTimeSlotCapacity as jest.Mock).mockResolvedValue({
        currentCount: 1,
        maxCapacity: 3,
        available: true,
      });
      
      const onBookingSuccess = jest.fn();
      
      render(
        <InteractivePlanner
          {...defaultProps}
          onBookingSuccess={onBookingSuccess}
        />
      );
      
      // Simulate drag and drop
      const dragButton = screen.getAllByText('Sürükleyin')[0];
      const timeSlots = screen.getAllByRole('button').filter(
        button => button.getAttribute('title')?.includes(':')
      );
      
      if (timeSlots.length > 0) {
        const slot = timeSlots[0];
        
        fireEvent.dragStart(dragButton, {
          dataTransfer: {
            setData: jest.fn(),
            effectAllowed: '',
          },
        });
        
        fireEvent.drop(slot, {
          dataTransfer: {
            getData: () => 'pkg-1',
          },
        });
        
        await waitFor(() => {
          expect(bookSingleAppointment).toHaveBeenCalled();
          expect(toast.success).toHaveBeenCalledWith('Randevu başarıyla oluşturuldu');
          expect(onBookingSuccess).toHaveBeenCalled();
        });
      }
    });

    it('handles booking errors', async () => {
      const mockBookingResult = {
        success: false,
        message: 'Booking failed',
        errors: ['Slot is full'],
      };
      
      (bookSingleAppointment as jest.Mock).mockResolvedValue(mockBookingResult);
      (checkTimeSlotCapacity as jest.Mock).mockResolvedValue({
        currentCount: 1,
        maxCapacity: 3,
        available: true,
      });
      
      const onBookingError = jest.fn();
      
      render(
        <InteractivePlanner
          {...defaultProps}
          onBookingError={onBookingError}
        />
      );
      
      // Simulate drag and drop
      const dragButton = screen.getAllByText('Sürükleyin')[0];
      const timeSlots = screen.getAllByRole('button').filter(
        button => button.getAttribute('title')?.includes(':')
      );
      
      if (timeSlots.length > 0) {
        const slot = timeSlots[0];
        
        fireEvent.dragStart(dragButton, {
          dataTransfer: {
            setData: jest.fn(),
            effectAllowed: '',
          },
        });
        
        fireEvent.drop(slot, {
          dataTransfer: {
            getData: () => 'pkg-1',
          },
        });
        
        await waitFor(() => {
          expect(toast.error).toHaveBeenCalledWith('Booking failed');
          expect(onBookingError).toHaveBeenCalled();
        });
      }
    });
  });

  describe('Accessibility', () => {
    it('has proper ARIA labels', () => {
      render(<InteractivePlanner {...defaultProps} />);
      
      expect(screen.getByRole('application')).toHaveAttribute(
        'aria-label',
        'Akıllı randevu planlayıcısı'
      );
      expect(screen.getByRole('grid')).toHaveAttribute(
        'aria-label',
        'Haftalık randevu takvimi'
      );
    });

    it('supports keyboard navigation', async () => {
      const user = userEvent.setup();
      render(<InteractivePlanner {...defaultProps} />);
      
      // Test tab navigation
      await user.tab();
      expect(document.activeElement).toBeInTheDocument();
    });
  });
});
