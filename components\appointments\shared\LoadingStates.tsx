"use client";

import React from "react";
import { motion } from "framer-motion";
import { Loader2, Calendar, Clock, Users } from "lucide-react";
import { cn } from "@/lib/utils";

// Skeleton loader for calendar grid
export const CalendarSkeleton = () => {
  return (
    <div className="space-y-4">
      {/* Header skeleton */}
      <div className="flex items-center justify-between">
        <div className="space-y-2">
          <div className="h-6 w-48 bg-muted rounded animate-pulse" />
          <div className="h-4 w-64 bg-muted rounded animate-pulse" />
        </div>
        <div className="flex gap-2">
          <div className="h-8 w-8 bg-muted rounded animate-pulse" />
          <div className="h-8 w-16 bg-muted rounded animate-pulse" />
          <div className="h-8 w-8 bg-muted rounded animate-pulse" />
        </div>
      </div>

      {/* Calendar grid skeleton */}
      <div className="grid grid-cols-8 gap-2">
        {/* Header row */}
        <div className="h-12 bg-muted rounded animate-pulse" />
        {Array.from({ length: 7 }).map((_, i) => (
          <div key={i} className="h-12 bg-muted rounded animate-pulse" />
        ))}
        
        {/* Time slots */}
        {Array.from({ length: 11 }).map((_, rowIndex) => (
          <React.Fragment key={rowIndex}>
            <div className="h-12 bg-muted rounded animate-pulse" />
            {Array.from({ length: 7 }).map((_, colIndex) => (
              <div key={colIndex} className="h-12 bg-muted rounded animate-pulse" />
            ))}
          </React.Fragment>
        ))}
      </div>

      {/* Packages skeleton */}
      <div className="fixed bottom-0 inset-x-0 p-4 bg-background border-t">
        <div className="flex gap-3 overflow-x-auto">
          {Array.from({ length: 3 }).map((_, i) => (
            <div key={i} className="min-w-[320px] h-20 bg-muted rounded-xl animate-pulse" />
          ))}
        </div>
      </div>
    </div>
  );
};

// Loading spinner with message
interface LoadingSpinnerProps {
  message?: string;
  size?: "sm" | "md" | "lg";
  className?: string;
}

export const LoadingSpinner = ({ 
  message = "Yükleniyor...", 
  size = "md",
  className 
}: LoadingSpinnerProps) => {
  const sizeClasses = {
    sm: "w-4 h-4",
    md: "w-6 h-6", 
    lg: "w-8 h-8"
  };

  return (
    <div className={cn("flex items-center justify-center gap-2", className)}>
      <Loader2 className={cn("animate-spin", sizeClasses[size])} />
      {message && (
        <span className="text-sm text-muted-foreground">{message}</span>
      )}
    </div>
  );
};

// Capacity loading indicator
export const CapacityLoader = () => {
  return (
    <motion.div
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      exit={{ opacity: 0 }}
      className="flex items-center gap-1"
    >
      <div className="w-2 h-2 bg-muted-foreground/40 rounded-full animate-pulse" />
      <span className="text-[10px] text-muted-foreground">...</span>
    </motion.div>
  );
};

// Booking progress indicator
interface BookingProgressProps {
  step: "validating" | "checking" | "booking" | "updating";
  className?: string;
}

export const BookingProgress = ({ step, className }: BookingProgressProps) => {
  const steps = {
    validating: { icon: Calendar, message: "Doğrulanıyor..." },
    checking: { icon: Users, message: "Kapasite kontrol ediliyor..." },
    booking: { icon: Clock, message: "Randevu oluşturuluyor..." },
    updating: { icon: Calendar, message: "Güncelleniyor..." },
  };

  const currentStep = steps[step];
  const Icon = currentStep.icon;

  return (
    <motion.div
      initial={{ opacity: 0, y: 10 }}
      animate={{ opacity: 1, y: 0 }}
      exit={{ opacity: 0, y: -10 }}
      className={cn(
        "flex items-center gap-2 px-3 py-2 bg-primary/10 rounded-lg border border-primary/20",
        className
      )}
    >
      <Icon className="w-4 h-4 text-primary animate-pulse" />
      <span className="text-sm text-primary font-medium">
        {currentStep.message}
      </span>
    </motion.div>
  );
};

// Retry button component
interface RetryButtonProps {
  onRetry: () => void;
  disabled?: boolean;
  retryCount?: number;
  maxRetries?: number;
  className?: string;
}

export const RetryButton = ({ 
  onRetry, 
  disabled = false, 
  retryCount = 0, 
  maxRetries = 3,
  className 
}: RetryButtonProps) => {
  const remainingRetries = maxRetries - retryCount;
  
  return (
    <motion.button
      whileHover={{ scale: 1.02 }}
      whileTap={{ scale: 0.98 }}
      onClick={onRetry}
      disabled={disabled || remainingRetries <= 0}
      className={cn(
        "inline-flex items-center gap-2 px-3 py-2 text-sm rounded-md transition-colors",
        disabled || remainingRetries <= 0
          ? "bg-muted text-muted-foreground cursor-not-allowed"
          : "bg-primary text-primary-foreground hover:bg-primary/90",
        className
      )}
    >
      <Loader2 className={cn("w-4 h-4", disabled && "animate-spin")} />
      {disabled ? "Deneniyor..." : `Tekrar Dene (${remainingRetries})`}
    </motion.button>
  );
};

// Error state component
interface ErrorStateProps {
  title?: string;
  message: string;
  onRetry?: () => void;
  retryCount?: number;
  maxRetries?: number;
  className?: string;
}

export const ErrorState = ({
  title = "Bir Hata Oluştu",
  message,
  onRetry,
  retryCount = 0,
  maxRetries = 3,
  className
}: ErrorStateProps) => {
  return (
    <motion.div
      initial={{ opacity: 0, y: 10 }}
      animate={{ opacity: 1, y: 0 }}
      className={cn(
        "flex flex-col items-center gap-3 p-4 text-center",
        className
      )}
    >
      <div className="w-12 h-12 rounded-full bg-destructive/10 flex items-center justify-center">
        <Calendar className="w-6 h-6 text-destructive" />
      </div>
      <div className="space-y-1">
        <h3 className="font-medium text-sm">{title}</h3>
        <p className="text-xs text-muted-foreground">{message}</p>
      </div>
      {onRetry && (
        <RetryButton
          onRetry={onRetry}
          retryCount={retryCount}
          maxRetries={maxRetries}
        />
      )}
    </motion.div>
  );
};

// Empty state component
interface EmptyStateProps {
  title?: string;
  message: string;
  action?: React.ReactNode;
  className?: string;
}

export const EmptyState = ({
  title = "Henüz Veri Yok",
  message,
  action,
  className
}: EmptyStateProps) => {
  return (
    <motion.div
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      className={cn(
        "flex flex-col items-center gap-3 p-8 text-center",
        className
      )}
    >
      <div className="w-16 h-16 rounded-full bg-muted flex items-center justify-center">
        <Calendar className="w-8 h-8 text-muted-foreground" />
      </div>
      <div className="space-y-1">
        <h3 className="font-medium">{title}</h3>
        <p className="text-sm text-muted-foreground">{message}</p>
      </div>
      {action && <div className="mt-4">{action}</div>}
    </motion.div>
  );
};
