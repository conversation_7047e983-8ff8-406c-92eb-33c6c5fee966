"use server";

import { createAdminClient } from "@/lib/supabase/admin";
import { revalidateTag } from "next/cache";
import type { Database } from "@/lib/supabase/types";

type Tables = Database["public"]["Tables"];
type Member = Tables["members"]["Row"];
export type MinimalMember = Pick<Member, "id" | "first_name">;

type MemberFlexPackage = Tables["member_flex_packages"]["Row"];
type FlexPackage = Tables["flex_packages"]["Row"];
type FlexPackageSession = Tables["flex_package_sessions"]["Row"];

interface MemberPackageWithDetails extends MemberFlexPackage {
  flex_package?: FlexPackage;
}

interface FlexPackageSessionWithDetails extends FlexPackageSession {
  member_package?: {
    flex_package?: FlexPackage;
  } | null;
  member?: Member | null;
}

// Helper function to find member ID from phone number using members table

export async function getMemberFromPhone(
  phone: string
): Promise<MinimalMember | null> {
  try {
    const supabase = createAdminClient();

    // Telefonu normalize et ve farklı formatları dene
    const digits = phone.replace(/\D/g, "");
    if (digits.length !== 10) {
      return null; // 10 hane değilse bulunamaz
    }

    // Farklı formatları dene
    const phoneFormats = [
      digits, // Raw digits: "5551234567"
      digits.replace(/^(\d{3})(\d{3})(\d{2})(\d{2})$/, "$1 $2 $3 $4"), // "555 123 45 67"
      digits.replace(/^(\d{3})(\d{3})(\d{4})$/, "$1 $2 $3"), // "************"
      `+90${digits}`, // "+905551234567"
      `+90 ${digits.replace(/^(\d{3})(\d{3})(\d{2})(\d{2})$/, "$1 $2 $3 $4")}`, // "+90 555 123 45 67"
      `+90 (${digits.slice(0, 3)}) ${digits.slice(3, 6)} ${digits.slice(
        6,
        8
      )} ${digits.slice(8)}`, // "+90 (555) 123 45 67"
    ];

    // Her format için veritabanında ara
    for (const format of phoneFormats) {
      const { data: memberData, error } = await supabase
        .from("members")
        .select("id, first_name")
        .eq("phone", format)
        .maybeSingle();

      if (error) {
        console.error(`Error finding member by phone format ${format}:`, error);
        continue;
      }

      if (memberData) {
        return memberData;
      }
    }

    return null;
  } catch (error) {
    console.error("Error finding member ID:", error);
    return null;
  }
}

// Server action to auto-complete past sessions
export async function autoCompletePastSessions(): Promise<number> {
  try {
    const supabase = createAdminClient();
    const { data, error } = await supabase.rpc("auto_complete_past_sessions");

    if (error) {
      console.error("Error auto-completing past sessions:", error);
      return 0;
    }

    return data || 0;
  } catch (error) {
    console.error("Error in autoCompletePastSessions:", error);
    return 0;
  }
}

// Server action to get member's all packages (active and completed)
export async function getMemberAllPackages(
  memberId: string
): Promise<MemberPackageWithDetails[]> {
  try {
    const supabase = createAdminClient();

    const { data: packages, error } = await supabase
      .from("member_flex_packages")
      .select(
        `
        *,
        flex_package:flex_packages(*)
      `
      )
      .eq("member_id", memberId)
      .in("status", ["active", "completed"])
      .order("created_at", { ascending: false });

    if (error) {
      console.error("Error fetching member packages:", error);
      return [];
    }

    return packages || [];
  } catch (error) {
    console.error("Error in getMemberAllPackages:", error);
    return [];
  }
}

// Server action to get member's existing scheduled sessions
export async function getMemberScheduledSessions(
  memberId: string
): Promise<string[]> {
  try {
    const supabase = createAdminClient();

    const { data: sessions, error } = await supabase
      .from("flex_package_sessions")
      .select("session_date")
      .eq("member_id", memberId)
      .eq("status", "scheduled")
      .gte("session_date", new Date().toISOString().split("T")[0]);

    if (error) {
      console.error("Error fetching scheduled sessions:", error);
      return [];
    }

    return sessions?.map((session) => session.session_date) || [];
  } catch (error) {
    console.error("Error in getMemberScheduledSessions:", error);
    return [];
  }
}

// Server action to get session details with relationships
export async function getSessionWithDetails(
  sessionId: string,
  memberId: string
): Promise<FlexPackageSessionWithDetails | null> {
  try {
    const supabase = createAdminClient();

    const { data: sessionData, error } = await supabase
      .from("flex_package_sessions")
      .select(
        `
        *,
        member_package:member_flex_packages(
          *,
          flex_package:flex_packages(*)
        ),
        member:members(*)
      `
      )
      .eq("id", sessionId)
      .eq("member_id", memberId)
      .single();

    if (error) {
      console.error("Error fetching session details:", error);
      return null;
    }

    return sessionData;
  } catch (error) {
    console.error("Error in getSessionWithDetails:", error);
    return null;
  }
}

// Server action to update session
export async function updateSession(
  sessionId: string,
  sessionDate: string,
  sessionTime: string
): Promise<boolean> {
  try {
    const supabase = createAdminClient();

    const { error } = await supabase
      .from("flex_package_sessions")
      .update({
        session_date: sessionDate,
        session_time: sessionTime,
        updated_at: new Date().toISOString(),
      })
      .eq("id", sessionId);

    if (error) {
      console.error("Error updating session:", error);
      return false;
    }

    return true;
  } catch (error) {
    console.error("Error in updateSession:", error);
    return false;
  }
}

// Server action to get member's sessions with details - OPTIMIZED VERSION
export async function getMemberSessions(
  memberId: string,
  status?: "scheduled" | "completed"
): Promise<FlexPackageSessionWithDetails[]> {
  try {
    const supabase = createAdminClient();

    let query = supabase
      .from("flex_package_sessions")
      .select(
        `
        *,
        member_package:member_flex_packages(
          *,
          flex_package:flex_packages(*)
        ),
        member:members(*)
      `
      )
      .eq("member_id", memberId)
      .order("session_date", { ascending: false })
      .order("session_time", { ascending: false });

    if (status) {
      query = query.eq("status", status);
    }

    const { data, error } = await query;

    if (error) {
      console.error("Error fetching member sessions:", error);
      return [];
    }

    return data || [];
  } catch (error) {
    console.error("Error in getMemberSessions:", error);
    return [];
  }
}

// Server action to delete a session and revalidate appointments page
export async function deleteSession(sessionId: string): Promise<boolean> {
  try {
    const supabase = createAdminClient();

    const { error } = await supabase
      .from("flex_package_sessions")
      .delete()
      .eq("id", sessionId);

    if (error) {
      console.error("Error deleting session:", error);
      return false;
    }

    // Invalidate the appointments route so Server Components refetch
    revalidateTag("appointments");
    return true;
  } catch (error) {
    console.error("Error in deleteSession:", error);
    return false;
  }
}

// Server action to check if member has existing appointment on a date
export async function checkExistingAppointment(
  memberId: string,
  sessionDate: string
): Promise<boolean> {
  try {
    const supabase = createAdminClient();

    const { data, error } = await supabase
      .from("flex_package_sessions")
      .select("id")
      .eq("member_id", memberId)
      .eq("session_date", sessionDate)
      .eq("status", "scheduled")
      .limit(1);

    if (error) {
      console.error("Error checking existing appointment:", error);
      return false;
    }

    return (data?.length || 0) > 0;
  } catch (error) {
    console.error("Error in checkExistingAppointment:", error);
    return false;
  }
}

// Server action to check time slot capacity - OPTIMIZED WITH BETTER ERROR HANDLING
export async function checkTimeSlotCapacity(
  sessionDate: string,
  sessionTime: string
): Promise<{
  available: boolean;
  currentCount: number;
  maxCapacity: number;
}> {
  try {
    const supabase = createAdminClient();

    const { data, error } = await supabase
      .from("flex_package_sessions")
      .select("id")
      .eq("session_date", sessionDate)
      .eq("session_time", sessionTime)
      .eq("status", "scheduled");

    if (error) {
      console.error("Error checking time slot capacity:", error);
      // Return default values instead of throwing error
      return {
        available: true, // Allow booking by default if there's an error
        currentCount: 0,
        maxCapacity: 3,
      };
    }

    const currentCount = data?.length || 0;
    const maxCapacity = 3;
    const available = currentCount < maxCapacity;

    return {
      available,
      currentCount,
      maxCapacity,
    };
  } catch (error) {
    console.error("Error in checkTimeSlotCapacity:", error);
    // Return safe defaults in case of unexpected errors
    return {
      available: true, // Allow booking by default if there's an error
      currentCount: 0,
      maxCapacity: 3,
    };
  }
}

// Server action to book a session
export async function bookSession(
  memberId: string,
  memberPackageId: string,
  sessionDate: string,
  sessionTime: string
): Promise<FlexPackageSession | null> {
  try {
    const supabase = createAdminClient();

    const session = {
      member_id: memberId,
      member_package_id: memberPackageId,
      session_date: sessionDate,
      session_time: sessionTime,
      status: "scheduled" as const,
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString(),
    };

    const { data, error } = await supabase
      .from("flex_package_sessions")
      .insert(session)
      .select()
      .single();

    if (error) {
      console.error("Error booking session:", error);
      return null;
    }

    return data;
  } catch (error) {
    console.error("Error in bookSession:", error);
    return null;
  }
}

// Server action to book multiple sessions at once
export async function bookMultipleAppointmentsAction(
  memberId: string,
  memberPackageId: string,
  appointments: Array<{ date: string; time: string }>
): Promise<{
  success: FlexPackageSession[];
  failed: Array<{ date: string; time: string; error: string }>;
}> {
  const success: FlexPackageSession[] = [];
  const failed: Array<{ date: string; time: string; error: string }> = [];

  // Her randevu için sırayla işlem yap
  for (const appointment of appointments) {
    try {
      // Kapasite kontrolü
      const capacityCheck = await checkTimeSlotCapacity(
        appointment.date,
        appointment.time
      );

      if (!capacityCheck.available) {
        failed.push({
          date: appointment.date,
          time: appointment.time,
          error: `Kapasite dolu (${capacityCheck.currentCount}/${capacityCheck.maxCapacity})`,
        });
        continue;
      }

      // Aynı güne randevu kontrolü (spor salonu için gerekli)
      const hasExistingAppointment = await checkExistingAppointment(
        memberId,
        appointment.date
      );

      if (hasExistingAppointment) {
        failed.push({
          date: appointment.date,
          time: appointment.time,
          error: "Bu tarihte zaten randevunuz var",
        });
        continue;
      }

      // Randevu oluştur
      const session = await bookSession(
        memberId,
        memberPackageId,
        appointment.date,
        appointment.time
      );

      if (session) {
        success.push(session);
      } else {
        failed.push({
          date: appointment.date,
          time: appointment.time,
          error: "Randevu oluşturulamadı",
        });
      }
    } catch (error) {
      failed.push({
        date: appointment.date,
        time: appointment.time,
        error: error instanceof Error ? error.message : "Bilinmeyen hata",
      });
    }
  }

  return { success, failed };
}
