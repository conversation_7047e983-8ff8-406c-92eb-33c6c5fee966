"use client";

import { useMemo, useState } from "react";
import { motion } from "framer-motion";
import { toast } from "sonner";
import {
  ChevronLeft,
  ChevronRight,
  GripVertical,
  ArrowLeft,
} from "lucide-react";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { cn } from "@/lib/utils";
import {
  WORKING_HOURS,
  isTimeSlotInPast,
} from "@/components/appointments/shared/appointment-utils";
import { checkTimeSlotCapacity } from "@/lib/actions/database";
import { bookSingleAppointment } from "@/lib/actions/appointments";
import type {
  FlexDashboardStats,
  FlexPackageGroup,
  ServerFlexPackageSessionWithDetails,
} from "@/components/appointments/FlexDashboard";
import Link from "next/link";

interface InteractivePlannerProps {
  initialFlexPackages: FlexPackageGroup[];
  initialBookedDates: string[];
}

interface TimeSlotCapacity {
  currentCount: number;
  maxCapacity: number;
  available: boolean;
}

export default function InteractivePlanner({
  initialFlexPackages,
  initialBookedDates,
}: InteractivePlannerProps) {
  const [flexPackages, setFlexPackages] =
    useState<FlexPackageGroup[]>(initialFlexPackages);
  const [bookedDates, setBookedDates] = useState<Set<string>>(
    new Set(initialBookedDates)
  );
  const [slotState, setSlotState] = useState<
    Record<string, { loading: boolean; cap?: TimeSlotCapacity }>
  >({});
  const stats: FlexDashboardStats = useMemo(() => {
    return flexPackages.reduce(
      (acc, p) => {
        acc.totalSessions += p.totalSessions;
        acc.usedSessions += p.usedSessions;
        acc.remainingSessions += p.remainingSessions;
        const sch = p.sessions.filter((s) => s.status === "scheduled").length;
        const comp = p.sessions.filter((s) => s.status === "completed").length;
        acc.scheduledSessions += sch;
        acc.completedSessions += comp;
        return acc;
      },
      {
        totalSessions: 0,
        usedSessions: 0,
        remainingSessions: 0,
        scheduledSessions: 0,
        completedSessions: 0,
      }
    );
  }, [flexPackages]);

  const [weekOffset, setWeekOffset] = useState(0);
  const weekDays = useMemo(() => {
    const start = new Date();
    start.setHours(0, 0, 0, 0);
    start.setDate(start.getDate() + weekOffset * 7);
    return Array.from({ length: 7 }, (_, i) => {
      const d = new Date(start);
      d.setDate(start.getDate() + i);
      return d.toISOString().split("T")[0];
    });
  }, [weekOffset]);

  const fetchCapacityIfNeeded = async (date: string, time: string) => {
    const key = `${date}-${time}`;
    if (slotState[key]?.cap || slotState[key]?.loading) return;
    setSlotState((s) => ({ ...s, [key]: { loading: true } }));
    try {
      const cap = await checkTimeSlotCapacity(date, time);
      setSlotState((s) => ({ ...s, [key]: { loading: false, cap } }));
    } catch {
      setSlotState((s) => ({
        ...s,
        [key]: {
          loading: false,
          cap: { currentCount: 0, maxCapacity: 3, available: true },
        },
      }));
    }
  };

  const handleDropToSlot = async (
    e: React.DragEvent<HTMLButtonElement>,
    date: string,
    time: string
  ) => {
    e.preventDefault();
    const memberPackageId = e.dataTransfer.getData("memberPackageId");
    if (!memberPackageId) return;

    if (isTimeSlotInPast(date, time)) {
      toast.error("Geçmiş saatlere randevu alınamaz");
      return;
    }
    if (bookedDates.has(date)) {
      toast.error("Aynı güne yalnızca bir randevu alabilirsiniz");
    }

    const key = `${date}-${time}`;
    await fetchCapacityIfNeeded(date, time);
    const cap = slotState[key]?.cap;
    if (cap && !cap.available) {
      toast.error("Kapasite dolu");
      return;
    }

    const fd = new FormData();
    fd.append("memberPackageId", memberPackageId);
    fd.append("sessionDate", date);
    fd.append("sessionTime", time);

    const res = await bookSingleAppointment(fd);
    if (!res.success) {
      toast.error(res.message);
      if (res.errors && res.errors.length)
        res.errors.forEach((m) => toast.error(m));
      return;
    }

    toast.success("Randevu oluşturuldu");

    // Optimistic local updates
    setBookedDates((prev) => new Set([...Array.from(prev), date]));
    setFlexPackages((prev) => {
      return prev.map((pkg) => {
        if (pkg.memberPackage.id !== memberPackageId) return pkg;
        const newSession: ServerFlexPackageSessionWithDetails = {
          id: Math.random().toString(36).slice(2),
          member_id: "",
          member_package_id: memberPackageId,
          session_date: date,
          session_time: time,
          status: "scheduled",
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString(),
          packageName: pkg.packageName,
        } as any;
        return {
          ...pkg,
          sessions: [...pkg.sessions, newSession],
          usedSessions: pkg.usedSessions, // used updates server-side when completed
          remainingSessions: Math.max(0, pkg.remainingSessions - 1),
        };
      });
    });
    // Update capacity cache for this slot
    setSlotState((s) => {
      const old = s[key]?.cap;
      if (!old) return s;
      const nextCount = Math.min(old.currentCount + 1, old.maxCapacity);
      const next: TimeSlotCapacity = {
        currentCount: nextCount,
        maxCapacity: old.maxCapacity,
        available: nextCount < old.maxCapacity,
      };
      return { ...s, [key]: { loading: false, cap: next } };
    });
  };

  return (
    <div className="space-y-4 py-3">
      {/* Header */}
      <div className="flex items-center justify-between gap-3">
        <div>
          <Link href="/appointments" className="h-8 px-3">
            <ArrowLeft className="w-4 h-4 mr-1" /> Geri Dön
          </Link>
          <h1 className="text-xl font-bold">Akıllı Planlayıcı</h1>
          <p className="text-sm text-muted-foreground">
            Paket haklarınızı takvim üzerinde sürükle-bırak ile planlayın
          </p>
        </div>
        <div className="flex items-center gap-2">
          <div className="flex items-center gap-1">
            <Button
              variant="ghost"
              size="icon"
              aria-label="Önceki hafta"
              onClick={() => setWeekOffset((o) => o - 1)}
              className="h-8 w-8"
            >
              <ChevronLeft className="w-4 h-4" />
            </Button>
            <Button
              variant="ghost"
              size="icon"
              aria-label="Sonraki hafta"
              onClick={() => setWeekOffset((o) => o + 1)}
              className="h-8 w-8"
            >
              <ChevronRight className="w-4 h-4" />
            </Button>
          </div>
        </div>
      </div>

      {/* Main calendar area - full width */}
      <div className="pb-32">
        {/* Weekly board */}
        <div>
          <div className="overflow-x-auto -mx-1 px-1">
            <div className="min-w-[900px]">
              <div
                className="grid gap-2"
                style={{
                  gridTemplateColumns: `120px repeat(${weekDays.length}, minmax(120px,1fr))`,
                  gridAutoRows: "minmax(48px,auto)",
                }}
              >
                {/* Header row: empty corner + dates */}
                <div className="px-2 py-2 text-xs sticky top-0 left-0 z-30 rounded-md border border-primary/30 bg-gradient-to-r from-primary/15 to-pink-200/20 text-foreground">
                  Saat/Tarih
                </div>
                {weekDays.map((date) => {
                  const isToday =
                    new Date(date).toDateString() === new Date().toDateString();
                  const dayDisabled = bookedDates.has(date);
                  const dow = new Date(date).getDay();
                  const isWeekend = dow === 0 || dow === 6;
                  return (
                    <div
                      key={`hdr-${date}`}
                      className={cn(
                        "px-2 py-2 text-xs font-medium sticky top-0 z-20 rounded-md border bg-gradient-to-b from-background/90 to-primary/10 backdrop-blur supports-[backdrop-filter]:bg-background/80 border-primary/20 text-foreground",
                        isToday && "ring-1 ring-primary/40 shadow-sm",
                        isWeekend && "bg-amber-50/60 dark:bg-amber-900/10",
                        dayDisabled && "opacity-80"
                      )}
                    >
                      <div className="font-semibold">
                        {new Date(date).toLocaleDateString("tr-TR", {
                          weekday: "short",
                        })}
                      </div>
                      <div className="text-[10px] text-muted-foreground">
                        {new Date(date).toLocaleDateString("tr-TR", {
                          day: "2-digit",
                          month: "2-digit",
                        })}
                      </div>
                      {dayDisabled && (
                        <Badge variant="outline" className="mt-1 text-[10px]">
                          Planlı
                        </Badge>
                      )}
                    </div>
                  );
                })}

                {/* Rows: each hour label + date cells */}
                {WORKING_HOURS.map((time) => (
                  <div key={`row-${time}`} className="contents">
                    <div className="px-3 py-2 border rounded-md sticky left-0 z-20 bg-primary/10 border-primary/20 text-primary">
                      <div className="text-sm font-semibold">{time}</div>
                    </div>

                    {weekDays.map((date) => {
                      const key = `${date}-${time}`;
                      const entry = slotState[key];
                      const cap = entry?.cap;
                      const isFull = cap ? !cap.available : false;
                      const isNearFull = cap
                        ? cap.available &&
                          cap.currentCount >= Math.max(1, cap.maxCapacity - 1)
                        : false;
                      const isToday =
                        new Date(date).toDateString() ===
                        new Date().toDateString();
                      const dow = new Date(date).getDay();
                      const isWeekend = dow === 0 || dow === 6;
                      const dayDisabled = bookedDates.has(date);
                      const disabled =
                        dayDisabled || isTimeSlotInPast(date, time) || isFull;
                      return (
                        <motion.button
                          key={key}
                          onMouseEnter={() =>
                            !disabled && fetchCapacityIfNeeded(date, time)
                          }
                          onDragOver={(e) => {
                            if (!disabled) e.preventDefault();
                          }}
                          onDrop={(e) => {
                            if (!disabled) handleDropToSlot(e, date, time);
                          }}
                          disabled={disabled}
                          title={`${new Date(date).toLocaleDateString(
                            "tr-TR"
                          )} ${time}`}
                          className={cn(
                            "text-left px-3 py-2 rounded-lg border text-xs transition-colors",
                            disabled
                              ? "opacity-50 cursor-not-allowed bg-muted/40 pointer-events-none"
                              : "hover:bg-primary/10 hover:border-primary/30 shadow-sm",
                            isFull
                              ? "bg-destructive/10 border-destructive/40"
                              : isNearFull
                              ? "bg-amber-500/10 border-amber-500/30"
                              : "border-border/50",
                            isToday && "ring-1 ring-primary/20",
                            isWeekend &&
                              !disabled &&
                              "bg-amber-50/40 dark:bg-amber-900/10"
                          )}
                        >
                          <div className="flex items-center justify-end">
                            <span className="inline-flex items-center gap-1">
                              {entry?.loading && (
                                <span className="w-2 h-2 rounded-full bg-muted-foreground/40 animate-pulse" />
                              )}
                              {cap && (
                                <>
                                  <span
                                    className={cn(
                                      "w-2 h-2 rounded-full",
                                      isFull
                                        ? "bg-destructive"
                                        : isNearFull
                                        ? "bg-amber-500"
                                        : "bg-emerald-500"
                                    )}
                                  />
                                  <span className="text-[10px] text-muted-foreground">
                                    {cap.currentCount}/{cap.maxCapacity}
                                  </span>
                                </>
                              )}
                            </span>
                          </div>
                        </motion.button>
                      );
                    })}
                  </div>
                ))}
              </div>
            </div>
          </div>
        </div>
      </div>
      {/* Fixed bottom packages dock */}
      <div className="fixed bottom-0 inset-x-0 z-40 border-t bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/80">
        <div className="mx-auto max-w-7xl px-3 py-2">
          <div className="flex items-stretch gap-3 overflow-x-auto">
            {flexPackages.map((pkg) => {
              const total = pkg.totalSessions;
              const planned = pkg.sessions.filter(
                (s) => s.status !== "completed"
              ).length;
              const progressPct =
                total > 0 ? Math.round((planned / total) * 100) : 0;
              const availableTokens = Math.max(0, pkg.remainingSessions);
              const isCompleted = pkg.memberPackage.status === "completed";
              return (
                <div
                  key={pkg.memberPackage.id}
                  className="group relative min-w-[320px] rounded-xl p-3 border border-primary/20 bg-gradient-to-br from-background via-primary/10 to-pink-100/20 dark:to-primary/5 shadow-sm hover:shadow-md transition"
                >
                  <div className="flex items-center gap-3">
                    <div className="flex items-center gap-2 min-w-[180px]">
                      <div className="text-sm font-semibold tracking-tight truncate">
                        {pkg.packageName}
                      </div>
                      <Badge
                        variant="outline"
                        className="text-xs px-2 py-0.5 rounded-full border-primary/30 text-primary"
                      >
                        {isCompleted ? "Tamamlandı" : `${availableTokens} hak`}
                      </Badge>
                    </div>

                    <div className="flex-1 h-2 bg-muted rounded-full overflow-hidden">
                      <div
                        className={cn(
                          "h-full rounded-full transition-all",
                          isCompleted
                            ? "bg-gradient-to-r from-emerald-500 via-emerald-400 to-emerald-600"
                            : "bg-gradient-to-r from-primary via-fuchsia-500 to-amber-500"
                        )}
                        style={{ width: `${progressPct}%` }}
                      />
                    </div>

                    {!isCompleted && (
                      <button
                        draggable={availableTokens > 0}
                        onDragStart={(e) =>
                          e.dataTransfer.setData(
                            "memberPackageId",
                            pkg.memberPackage.id
                          )
                        }
                        className={cn(
                          "px-2.5 py-1.5 text-xs rounded-md inline-flex items-center gap-1 select-none border bg-background transition",
                          availableTokens > 0
                            ? "cursor-grab active:cursor-grabbing hover:bg-muted"
                            : "cursor-not-allowed opacity-50"
                        )}
                        title="Bu paketten bir seansı takvime sürükleyin"
                        disabled={availableTokens === 0}
                      >
                        <GripVertical className="w-4 h-4" /> Sürükleyin
                      </button>
                    )}
                  </div>
                </div>
              );
            })}
          </div>
        </div>
      </div>
    </div>
  );
}
