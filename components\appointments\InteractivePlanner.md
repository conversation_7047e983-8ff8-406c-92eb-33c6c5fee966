# InteractivePlanner Component

## Overview

The `InteractivePlanner` is a comprehensive, accessible, and feature-rich appointment scheduling component that allows users to drag and drop package sessions onto a weekly calendar view. It provides real-time capacity checking, error handling, and a smooth user experience.

## Features

### Core Functionality

- **Drag & Drop Interface**: Intuitive drag-and-drop from package cards to calendar slots
- **Real-time Capacity Checking**: Live updates of slot availability
- **Weekly Calendar View**: Navigate through weeks with navigation buttons
- **Optimistic Updates**: Immediate UI feedback with server synchronization
- **Error Recovery**: Comprehensive error handling with retry mechanisms

### Accessibility

- **ARIA Labels**: Full screen reader support
- **Focus Management**: Proper focus handling for accessibility
- **Focus Management**: Proper focus handling throughout interactions
- **High Contrast**: Support for high contrast themes
- **Responsive Design**: Mobile-friendly interface

### Performance

- **Capacity Caching**: Intelligent caching of capacity data
- **Request Debouncing**: Optimized API calls
- **Memory Management**: Proper cleanup of resources
- **Lazy Loading**: On-demand capacity fetching

### Error Handling

- **Network Monitoring**: Offline/online state detection
- **Retry Logic**: Exponential backoff for failed requests
- **Error Boundaries**: Component-level error isolation
- **User Feedback**: Clear error messages and recovery options

## Props

```typescript
interface InteractivePlannerProps {
  initialFlexPackages: FlexPackageGroup[];
  initialBookedDates: string[];
  onBookingSuccess?: (booking: BookingSuccessData) => void;
  onBookingError?: (error: BookingError) => void;
  maxWeeksAhead?: number; // Default: 8
  maxWeeksBehind?: number; // Default: 0
}
```

### Prop Details

- `initialFlexPackages`: Array of user's available packages with session counts
- `initialBookedDates`: Pre-existing booked dates to prevent double booking
- `onBookingSuccess`: Callback fired when booking succeeds
- `onBookingError`: Callback fired when booking fails
- `maxWeeksAhead`: Maximum weeks user can navigate forward
- `maxWeeksBehind`: Maximum weeks user can navigate backward

## Usage

```tsx
import InteractivePlanner from "@/components/appointments/InteractivePlanner";

function AppointmentPage() {
  const handleBookingSuccess = (booking) => {
    console.log("Booking successful:", booking);
    // Handle success (e.g., redirect, show confirmation)
  };

  const handleBookingError = (error) => {
    console.error("Booking failed:", error);
    // Handle error (e.g., show error modal, log to service)
  };

  return (
    <InteractivePlanner
      initialFlexPackages={userPackages}
      initialBookedDates={existingBookings}
      onBookingSuccess={handleBookingSuccess}
      onBookingError={handleBookingError}
      maxWeeksAhead={12}
      maxWeeksBehind={1}
    />
  );
}
```

## Component Architecture

### Main Components

- `InteractivePlanner`: Main wrapper with error boundary
- `InteractivePlannerCore`: Core component logic
- `ErrorBoundary`: Error isolation and recovery
- `LoadingStates`: Various loading and error state components

### Custom Hooks

- `useInteractivePlanner`: Main state management hook
- `useErrorHandler`: Error handling utilities

### Utilities

- `appointment-utils.ts`: Shared utilities and types
- `ErrorBoundary.tsx`: Error boundary component
- `LoadingStates.tsx`: Loading and error state components

## State Management

The component uses a combination of React state and refs for optimal performance:

### State Variables

- `flexPackages`: User's available packages
- `bookedDates`: Set of already booked dates
- `slotState`: Capacity and loading state for each time slot
- `dragState`: Current drag operation state
- `weekOffset`: Current week offset from today
- `isBooking`: Global booking state
- `bookingStep`: Current step in booking process
- `errorState`: Error handling state
- `networkState`: Network connectivity state

### Refs

- `capacityCache`: Cached capacity data
- `abortController`: Request cancellation
- `retryTimeouts`: Retry operation timeouts

## Error Handling Strategy

### Error Types

1. **Network Errors**: Connection issues, timeouts
2. **Validation Errors**: Invalid data, business rule violations
3. **Capacity Errors**: Slot full, double booking attempts
4. **System Errors**: Unexpected server errors

### Recovery Mechanisms

1. **Automatic Retry**: Exponential backoff for transient errors
2. **Manual Retry**: User-initiated retry buttons
3. **Graceful Degradation**: Fallback to basic functionality
4. **Error Boundaries**: Component isolation and recovery

## Performance Optimizations

### Caching Strategy

- **Capacity Cache**: In-memory cache with date-time keys
- **Request Deduplication**: Prevent duplicate API calls
- **Cache Invalidation**: Smart cache clearing on updates

### Memory Management

- **Cleanup on Unmount**: Abort pending requests
- **Timeout Clearing**: Clear retry timeouts
- **Event Listener Removal**: Clean up event listeners

### Rendering Optimizations

- **Memoized Calculations**: useMemo for expensive computations
- **Callback Memoization**: useCallback for event handlers
- **Conditional Rendering**: Minimize unnecessary re-renders

## Testing Strategy

### Unit Tests

- Component rendering
- State management
- Event handling
- Error scenarios

### Integration Tests

- Drag and drop functionality
- API integration
- Error boundary behavior
- User interaction flows

### Accessibility Tests

- Screen reader compatibility
- Tab navigation
- Focus management
- ARIA attributes

## Browser Support

- **Modern Browsers**: Chrome 90+, Firefox 88+, Safari 14+, Edge 90+
- **Mobile**: iOS Safari 14+, Chrome Mobile 90+
- **Accessibility**: NVDA, JAWS, VoiceOver support

## Dependencies

### Required

- React 18+
- Framer Motion (animations)
- Lucide React (icons)
- Sonner (toast notifications)

### Optional

- Tailwind CSS (styling)
- TypeScript (type safety)

## Migration Guide

### From v1.x to v2.x

1. Update prop names (breaking changes)
2. Add error handling callbacks
3. Update interaction handling
4. Test accessibility features

## Contributing

### Development Setup

1. Install dependencies: `npm install`
2. Start development server: `npm run dev`
3. Run tests: `npm test`
4. Check accessibility: `npm run a11y`

### Code Style

- Use TypeScript for type safety
- Follow React best practices
- Add comprehensive error handling
- Include accessibility attributes
- Write unit tests for new features

## Troubleshooting

### Common Issues

1. **Drag not working**: Check browser compatibility
2. **Capacity not loading**: Verify API endpoints
3. **Interactions not working**: Check event handlers
4. **Accessibility issues**: Run accessibility audit

### Debug Mode

Set `NODE_ENV=development` for additional logging and error details.
