"use client";

import { motion } from "framer-motion";
import { Target, Calendar, CheckCircle, Award } from "lucide-react";

interface FlexDashboardStats {
  totalSessions: number;
  usedSessions: number;
  remainingSessions: number;
  scheduledSessions: number;
  completedSessions: number;
}

interface StatsGridProps {
  stats: FlexDashboardStats;
}

const fadeInUp = {
  initial: { opacity: 0, y: 20 },
  animate: { opacity: 1, y: 0 },
};

export default function StatsGrid({ stats }: StatsGridProps) {
  const statsData = [
    {
      title: "Kalan",
      value: stats.remainingSessions,
      icon: Target,
      color: "text-emerald-600",
      bgColor: "bg-emerald-50 dark:bg-emerald-950/20",
      priority: 1,
    },
    {
      title: "Planlanmış",
      value: stats.scheduledSessions,
      icon: Calendar,
      color: "text-blue-600",
      bgColor: "bg-blue-50 dark:bg-blue-950/20",
      priority: 1,
    },
    {
      title: "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>",
      value: stats.completedSessions,
      icon: CheckCircle,
      color: "text-violet-600",
      bgColor: "bg-violet-50 dark:bg-violet-950/20",
      priority: 2,
    },
  ];

  // Sort by priority (1 = highest priority)
  const sortedStats = [...statsData].sort((a, b) => a.priority - b.priority);

  return (
    <motion.section
      variants={fadeInUp}
      initial="initial"
      animate="animate"
      className="space-y-3"
    >
      <div className="grid grid-cols-2 lg:grid-cols-3 gap-2">
        {sortedStats.map((stat, index) => (
          <motion.div
            key={stat.title}
            variants={fadeInUp}
            transition={{ delay: index * 0.05 }}
            className="group"
          >
            <div
              className={`relative overflow-hidden rounded-lg p-3 ${stat.bgColor} border border-border/20 hover:border-border/40 transition-all duration-200 h-full`}
            >
              <div className="flex items-center justify-between mb-2">
                <stat.icon className={`w-4 h-4 ${stat.color}`} />
                <p className="text-lg font-bold text-foreground group-hover:scale-105 transition-transform origin-right">
                  {stat.value}
                </p>
              </div>
              <p className="text-xs font-medium text-muted-foreground">
                {stat.title}
              </p>
            </div>
          </motion.div>
        ))}
      </div>
    </motion.section>
  );
}
