"use client";

import { motion } from "framer-motion";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import Link from "next/link";
import { Calendar, CheckCircle, Edit3, Trash2, Clock } from "lucide-react";
import { cn } from "@/lib/utils";
import type { Member } from "@/lib/supabase/types";

interface ServerFlexPackageSessionWithDetails {
  id: string;
  member_id: string;
  member_package_id: string;
  session_date: string;
  session_time: string;
  status: "scheduled" | "completed";
  notes?: string | null;
  created_at: string;
  updated_at: string;
  member_package?: {
    flex_package?: {
      id: string;
      name: string;
      description: string | null;
      duration_days: number;
      session_count: number;
      price: number;
      is_active: boolean;
    } | null;
  } | null;
  member?: Member | null;
  packageName?: string;
}

interface SessionItemProps {
  session: ServerFlexPackageSessionWithDetails;
  packageName?: string;
  onDelete?: () => void;
  canDelete?: boolean;
  isHistorical?: boolean;
  index?: number;
}

const formatDate = (dateString: string) =>
  new Date(dateString).toLocaleDateString("tr-TR", {
    day: "numeric",
    month: "long",
    year: "numeric",
  });

const formatTime = (timeString: string) => timeString.slice(0, 5);

// Calculate days remaining until appointment
const calculateDaysUntil = (dateString: string): number => {
  const today = new Date();
  today.setHours(0, 0, 0, 0);
  const appointmentDate = new Date(dateString);
  appointmentDate.setHours(0, 0, 0, 0);
  const diffTime = appointmentDate.getTime() - today.getTime();
  const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
  return diffDays;
};

export default function SessionItem({
  session,
  packageName,
  onDelete,
  canDelete,
  isHistorical = false,
  index = 0,
}: SessionItemProps) {
  const isCompleted = session.status === "completed";
  const daysUntil = calculateDaysUntil(session.session_date);

  // Format days until text
  const formatDaysUntilText = (): string => {
    if (daysUntil === 0) return "Bugün";
    if (daysUntil === 1) return "Yarın";
    return `${daysUntil} gün sonra`;
  };

  return (
    <motion.div
      initial={isHistorical ? { opacity: 0, x: -20 } : { opacity: 1, x: 0 }}
      animate={{ opacity: 1, x: 0 }}
      transition={{
        duration: 0.3,
        ease: "easeOut",
        delay: isHistorical ? index * 0.05 : 0,
      }}
      className={cn(
        "group relative rounded-md border transition-all duration-200 hover:border-border",
        isCompleted
          ? "bg-emerald-50/30 border-emerald-200/40 dark:bg-emerald-950/10 dark:border-emerald-800/20"
          : "bg-card border-border/50"
      )}
    >
      <div className="flex items-center justify-between p-2 gap-2">
        <div className="flex items-center gap-2 min-w-0 flex-1">
          <div
            className={cn(
              "w-5 h-5 rounded flex items-center justify-center text-white text-xs",
              isCompleted
                ? "bg-emerald-500"
                : daysUntil === 0
                ? "bg-rose-500"
                : daysUntil <= 1
                ? "bg-amber-500"
                : "bg-primary"
            )}
          >
            {isCompleted ? (
              <CheckCircle className="w-2.5 h-2.5" />
            ) : (
              <Calendar className="w-2.5 h-2.5" />
            )}
          </div>

          <div className="min-w-0 flex-1">
            <div className="flex items-center gap-2">
              <p className="font-medium text-foreground text-xs">
                {formatDate(session.session_date)}
              </p>
              <span className="text-muted-foreground text-xs">
                {formatTime(session.session_time)}
              </span>
              {!isCompleted && (
                <Badge
                  variant="secondary"
                  className={cn(
                    "text-xs px-1.5 py-0",
                    daysUntil === 0
                      ? "bg-rose-100 text-rose-700 dark:bg-rose-950/30 dark:text-rose-400"
                      : daysUntil === 1
                      ? "bg-amber-100 text-amber-700 dark:bg-amber-950/30 dark:text-amber-400"
                      : daysUntil <= 3
                      ? "bg-yellow-100 text-yellow-700 dark:bg-yellow-950/30 dark:text-yellow-400"
                      : "bg-emerald-100 text-emerald-700 dark:bg-emerald-950/30 dark:text-emerald-400"
                  )}
                >
                  {formatDaysUntilText()}
                </Badge>
              )}
            </div>
            {packageName && (
              <p className="text-xs text-muted-foreground truncate mt-0.5">
                {packageName}
              </p>
            )}
          </div>
        </div>

        <div className="flex items-center gap-1">
          {!isCompleted && canDelete && (
            <>
              <Link href={`/appointments/${session.id}/edit`}>
                <Button
                  variant="ghost"
                  size="sm"
                  className="h-6 w-6 p-0 hover:bg-primary/10"
                >
                  <Edit3 className="w-2.5 h-2.5" />
                </Button>
              </Link>
              <Button
                variant="ghost"
                size="sm"
                onClick={onDelete}
                className="h-6 w-6 p-0 text-destructive hover:bg-destructive/10"
              >
                <Trash2 className="w-2.5 h-2.5" />
              </Button>
            </>
          )}
          {!isCompleted && !canDelete && (
            <Badge
              variant="outline"
              className="text-xs bg-amber-50 text-amber-700 border-amber-300 dark:bg-amber-950/20 dark:text-amber-400 dark:border-amber-800 px-1.5 py-0"
            >
              <Clock className="w-2.5 h-2.5 mr-1" />
              Yakında
            </Badge>
          )}
          {isCompleted && (
            <Badge
              variant="secondary"
              className="text-xs bg-emerald-100 text-emerald-700 dark:bg-emerald-950/30 dark:text-emerald-400 px-1.5 py-0"
            >
              <CheckCircle className="w-2.5 h-2.5 mr-1" />
              Tamamlandı
            </Badge>
          )}
        </div>
      </div>
    </motion.div>
  );
}
